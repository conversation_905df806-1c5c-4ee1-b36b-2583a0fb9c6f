// 多终端协同操作页面JavaScript

// 数据管理器
class MultiTerminalDataManager {
    constructor() {
        this.data = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortField = '';
        this.sortOrder = 'asc';
    }

    async loadData() {
        try {
            const response = await fetch('/data/multi_terminal.json');
            if (!response.ok) {
                throw new Error('Failed to load data');
            }
            const jsonData = await response.json();
            this.data = jsonData.multi_terminal_sessions || [];
            this.filteredData = [...this.data];
            this.updateStats();
            this.renderTable();
            this.renderPagination();
        } catch (error) {
            console.error('Error loading data:', error);
            this.showError('加载数据失败，请刷新页面重试');
        }
    }

    updateStats() {
        const total = this.data.length;
        const online = this.data.filter(item => item.status === '在线').length;
        const synced = this.data.filter(item => item.sync_status === '已同步').length;
        const lowBattery = this.data.filter(item => parseInt(item.battery_level) < 20).length;

        const totalElement = document.getElementById('totalTerminals');
        const onlineElement = document.getElementById('onlineTerminals');
        const syncedElement = document.getElementById('syncedTerminals');
        const lowBatteryElement = document.getElementById('lowBatteryTerminals');

        if (totalElement) totalElement.textContent = total;
        if (onlineElement) onlineElement.textContent = online;
        if (syncedElement) syncedElement.textContent = synced;
        if (lowBatteryElement) lowBatteryElement.textContent = lowBattery;
    }

    filter(filters) {
        this.filteredData = this.data.filter(item => {
            return Object.keys(filters).every(key => {
                if (!filters[key]) return true;
                return item[key] === filters[key];
            });
        });
        this.currentPage = 1;
        this.renderTable();
        this.renderPagination();
    }

    search(query) {
        if (!query) {
            this.filteredData = [...this.data];
        } else {
            this.filteredData = this.data.filter(item =>
                item.terminal_id.toLowerCase().includes(query.toLowerCase()) ||
                item.device_name.toLowerCase().includes(query.toLowerCase()) ||
                item.location.toLowerCase().includes(query.toLowerCase()) ||
                item.operator.toLowerCase().includes(query.toLowerCase())
            );
        }
        this.currentPage = 1;
        this.renderTable();
        this.renderPagination();
    }

    sort(field) {
        if (this.sortField === field) {
            this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortOrder = 'asc';
        }

        this.filteredData.sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];

            if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }

            if (aVal < bVal) return this.sortOrder === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortOrder === 'asc' ? 1 : -1;
            return 0;
        });

        this.renderTable();
        this.updateSortIndicators();
    }

    updateSortIndicators() {
        document.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.textContent = '';
        });

        const currentHeader = document.querySelector(`th[onclick="sortTable('${this.sortField}')"] .sort-indicator`);
        if (currentHeader) {
            currentHeader.textContent = this.sortOrder === 'asc' ? '↑' : '↓';
        }
    }

    renderTable() {
        const tbody = document.getElementById('tableBody');
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        const pageData = this.filteredData.slice(start, end);

        if (pageData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="empty-state">
                        <div class="empty-state-icon">📱</div>
                        <div class="empty-state-text">暂无数据</div>
                        <div class="empty-state-desc">没有找到符合条件的终端设备</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = pageData.map(item => `
            <tr>
                <td>${item.terminal_id}</td>
                <td>${item.device_name}</td>
                <td><span class="device-type-badge ${item.device_type.toLowerCase()}">${item.device_type}</span></td>
                <td>${item.location}</td>
                <td><span class="status-badge ${item.status === '在线' ? 'online' : item.status === '离线' ? 'offline' : 'maintenance'}">${item.status}</span></td>
                <td>
                    <div class="battery-indicator">
                        <div class="battery-level" style="width: ${item.battery_level}%"></div>
                        <span class="battery-text">${item.battery_level}%</span>
                    </div>
                </td>
                <td><span class="sync-status ${item.sync_status === '已同步' ? 'synced' : item.sync_status === '同步中' ? 'syncing' : 'error'}">${item.sync_status}</span></td>
                <td>${item.operator}</td>
                <td>${item.last_sync}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-action view" onclick="viewTerminal('${item.terminal_id}')">查看</button>
                        ${item.sync_status === '同步失败' ? `<button class="btn-action sync" onclick="syncTerminal('${item.terminal_id}')">同步</button>` : ''}
                        ${item.status === '在线' ? `<button class="btn-action remote" onclick="remoteControl('${item.terminal_id}')">远程</button>` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderPagination() {
        const container = document.getElementById('paginationContainer');
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);

        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = '<div class="pagination">';

        // 上一页
        html += `<button class="page-btn" ${this.currentPage === 1 ? 'disabled' : ''} onclick="changePage(${this.currentPage - 1})">上一页</button>`;

        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                html += `<button class="page-btn active">${i}</button>`;
            } else if (i === 1 || i === totalPages || Math.abs(i - this.currentPage) <= 2) {
                html += `<button class="page-btn" onclick="changePage(${i})">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                html += `<span class="page-ellipsis">...</span>`;
            }
        }

        // 下一页
        html += `<button class="page-btn" ${this.currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${this.currentPage + 1})">下一页</button>`;

        html += '</div>';
        container.innerHTML = html;
    }

    changePage(page) {
        this.currentPage = page;
        this.renderTable();
        this.renderPagination();
    }

    showError(message) {
        showToast(message, 'error');
    }
}

// 全局变量
let dataManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    dataManager = new MultiTerminalDataManager();
    dataManager.loadData();

    // 绑定事件监听器
    bindEventListeners();
});

// 绑定事件监听器
function bindEventListeners() {
    // 搜索框
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            dataManager.search(this.value);
        }, 300));
    }

    // 筛选器
    const filters = ['statusFilter', 'deviceTypeFilter', 'syncStatusFilter'];
    filters.forEach(filterId => {
        const filterElement = document.getElementById(filterId);
        if (filterElement) {
            filterElement.addEventListener('change', handleFilter);
        }
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 处理筛选
function handleFilter() {
    const filters = {
        status: document.getElementById('statusFilter')?.value || '',
        device_type: document.getElementById('deviceTypeFilter')?.value || '',
        sync_status: document.getElementById('syncStatusFilter')?.value || ''
    };
    dataManager.filter(filters);
}

// 排序表格
function sortTable(field) {
    dataManager.sort(field);
}

// 切换页面
function changePage(page) {
    dataManager.changePage(page);
}

// 刷新数据
function refreshData() {
    dataManager.loadData();
    showToast('数据已刷新', 'success');
}

// 导出数据
function exportData() {
    const csvContent = convertToCSV(dataManager.filteredData);
    downloadCSV(csvContent, 'multi_terminal.csv');
    showToast('数据导出成功', 'success');
}

// 转换为CSV格式
function convertToCSV(data) {
    const headers = ['终端编号', '设备名称', '设备类型', '位置', '状态', '电池电量', '同步状态', '操作员', '最后同步'];
    const rows = data.map(item => [
        item.terminal_id,
        item.device_name,
        item.device_type,
        item.location,
        item.status,
        item.battery_level + '%',
        item.sync_status,
        item.operator,
        item.last_sync
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// 下载CSV文件
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 显示新建终端模态框
function showNewTerminalModal() {
    const modal = document.getElementById('newTerminalModal');
    if (modal) {
        modal.style.display = 'block';
    }
}

// 查看终端详情
function viewTerminal(terminalId) {
    const terminal = dataManager.data.find(item => item.terminal_id === terminalId);
    if (!terminal) return;

    const modal = document.getElementById('viewTerminalModal');
    if (modal) {
        // 填充详情数据
        document.getElementById('viewTerminalId').textContent = terminal.terminal_id;
        document.getElementById('viewDeviceName').textContent = terminal.device_name;
        document.getElementById('viewDeviceType').innerHTML = `<span class="device-type-badge ${terminal.device_type.toLowerCase()}">${terminal.device_type}</span>`;
        document.getElementById('viewLocation').textContent = terminal.location;
        document.getElementById('viewStatus').innerHTML = `<span class="status-badge ${terminal.status === '在线' ? 'online' : terminal.status === '离线' ? 'offline' : 'maintenance'}">${terminal.status}</span>`;
        document.getElementById('viewBatteryLevel').textContent = terminal.battery_level + '%';
        document.getElementById('viewSyncStatus').innerHTML = `<span class="sync-status ${terminal.sync_status === '已同步' ? 'synced' : terminal.sync_status === '同步中' ? 'syncing' : 'error'}">${terminal.sync_status}</span>`;
        document.getElementById('viewOperator').textContent = terminal.operator;
        document.getElementById('viewLastSync').textContent = terminal.last_sync;

        modal.style.display = 'block';
    }
}

// 同步终端
function syncTerminal(terminalId) {
    if (confirm('确定要同步此终端吗？')) {
        const terminal = dataManager.data.find(item => item.terminal_id === terminalId);
        if (terminal) {
            terminal.sync_status = '同步中';
            terminal.last_sync = new Date().toLocaleString();
            dataManager.updateStats();
            dataManager.renderTable();
            showToast('终端同步已开始', 'success');

            // 模拟同步完成
            setTimeout(() => {
                terminal.sync_status = '已同步';
                dataManager.updateStats();
                dataManager.renderTable();
                showToast('终端同步完成', 'success');
            }, 3000);
        }
    }
}

// 远程控制
function remoteControl(terminalId) {
    const terminal = dataManager.data.find(item => item.terminal_id === terminalId);
    if (!terminal) return;

    const modal = document.getElementById('remoteControlModal');
    if (modal) {
        document.getElementById('remoteTerminalId').value = terminalId;
        document.getElementById('remoteDeviceName').textContent = terminal.device_name;
        modal.style.display = 'block';
    }
}

// 关闭模态框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// 提交新建终端
function submitNewTerminal() {
    const formData = new FormData(document.getElementById('newTerminalForm'));
    const data = Object.fromEntries(formData);

    // 模拟提交操作
    console.log('提交新终端:', data);
    closeModal('newTerminalModal');
    showToast('终端已添加', 'success');
}

// 执行远程命令
function executeRemoteCommand() {
    const command = document.getElementById('remoteCommand').value;
    const terminalId = document.getElementById('remoteTerminalId').value;

    if (!command.trim()) {
        showToast('请输入命令', 'warning');
        return;
    }

    // 模拟执行命令
    console.log('执行远程命令:', { terminalId, command });
    showToast('命令已发送', 'success');

    // 清空命令输入
    document.getElementById('remoteCommand').value = '';
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 24px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideInRight 0.3s ease;
    `;

    switch (type) {
        case 'success':
            toast.style.backgroundColor = '#52c41a';
            break;
        case 'error':
            toast.style.backgroundColor = '#ff4d4f';
            break;
        case 'warning':
            toast.style.backgroundColor = '#faad14';
            break;
        default:
            toast.style.backgroundColor = '#1890ff';
    }

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
};