{"auto_inspections": [{"id": "AI-20240115-001", "name": "日常巡检-A区", "type": "日常巡检", "status": "已完成", "start_time": "2024-01-15 08:30:00", "end_time": "2024-01-15 09:15:00", "duration": "45", "location": "A区-货架001", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "0", "route_efficiency": "95.8", "route_points": "A1,A2,A3,A4,A5,A6", "description": "A区货架商品陈列检查"}, {"id": "AI-20240115-002", "name": "冷链区温度巡检", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-15 09:30:00", "end_time": "2024-01-15 10:00:00", "duration": "30", "location": "B区-冷柜002", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "1", "route_efficiency": "92.3", "route_points": "B1,B2,B3,B4", "description": "冷链区温度监控检查"}, {"id": "AI-20240115-003", "name": "商品有效期检查", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-15 10:15:00", "end_time": "2024-01-15 11:00:00", "duration": "45", "location": "C区-货架003", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "2", "route_efficiency": "88.5", "route_points": "C1,C2,C3,C4,C5", "description": "商品有效期检查"}, {"id": "AI-20240115-004", "name": "商品陈列合规性检查", "type": "日常巡检", "status": "已完成", "start_time": "2024-01-15 11:15:00", "end_time": "2024-01-15 12:00:00", "duration": "45", "location": "D区-货架004", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "0", "route_efficiency": "96.2", "route_points": "D1,D2,D3,D4", "description": "商品陈列合规性检查"}, {"id": "AI-20240115-005", "name": "价格标签检查", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-15 13:00:00", "end_time": "2024-01-15 13:45:00", "duration": "45", "location": "E区-货架005", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "3", "route_efficiency": "91.7", "route_points": "E1,E2,E3,E4,E5", "description": "价格标签一致性检查"}, {"id": "AI-20240115-006", "name": "库存盘点", "type": "定期巡检", "status": "已完成", "start_time": "2024-01-15 14:00:00", "end_time": "2024-01-15 15:30:00", "duration": "90", "location": "F区-货架006", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "5", "route_efficiency": "87.3", "route_points": "F1,F2,F3,F4,F5,F6,F7,F8", "description": "F区商品库存盘点"}, {"id": "AI-20240115-007", "name": "促销区检查", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-15 15:45:00", "end_time": "2024-01-15 16:15:00", "duration": "30", "location": "G区-促销台007", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "0", "route_efficiency": "97.1", "route_points": "G1,G2,G3", "description": "促销区商品陈列检查"}, {"id": "AI-20240115-008", "name": "收银区巡检", "type": "日常巡检", "status": "已完成", "start_time": "2024-01-15 16:30:00", "end_time": "2024-01-15 17:00:00", "duration": "30", "location": "H区-收银台008", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "0", "route_efficiency": "98.5", "route_points": "H1,H2,H3,H4", "description": "收银区设备运行状态检查"}, {"id": "AI-20240116-001", "name": "日常巡检-B区", "type": "日常巡检", "status": "已完成", "start_time": "2024-01-16 08:30:00", "end_time": "2024-01-16 09:15:00", "duration": "45", "location": "B区-货架009", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "1", "route_efficiency": "94.2", "route_points": "B1,B2,B3,B4,B5,B6", "description": "B区货架商品陈列检查"}, {"id": "AI-20240116-002", "name": "生鲜区温度巡检", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-16 09:30:00", "end_time": "2024-01-16 10:00:00", "duration": "30", "location": "I区-生鲜柜010", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "0", "route_efficiency": "95.8", "route_points": "I1,I2,I3,I4", "description": "生鲜区温度监控检查"}, {"id": "AI-20240116-003", "name": "商品有效期检查", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-16 10:15:00", "end_time": "2024-01-16 11:00:00", "duration": "45", "location": "J区-货架011", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "4", "route_efficiency": "86.9", "route_points": "J1,J2,J3,J4,J5", "description": "商品有效期检查"}, {"id": "AI-20240116-004", "name": "商品陈列合规性检查", "type": "日常巡检", "status": "已完成", "start_time": "2024-01-16 11:15:00", "end_time": "2024-01-16 12:00:00", "duration": "45", "location": "K区-货架012", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "2", "route_efficiency": "93.4", "route_points": "K1,K2,K3,K4", "description": "商品陈列合规性检查"}, {"id": "AI-20240116-005", "name": "价格标签检查", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-16 13:00:00", "end_time": "2024-01-16 13:45:00", "duration": "45", "location": "L区-货架013", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "1", "route_efficiency": "94.3", "route_points": "L1,L2,L3,L4,L5", "description": "价格标签一致性检查"}, {"id": "AI-20240116-006", "name": "库存盘点", "type": "定期巡检", "status": "进行中", "start_time": "2024-01-16 14:00:00", "end_time": null, "duration": "0", "location": "M区-货架014", "executor": "AI机器人", "completion_rate": 65, "anomaly_count": "3", "route_efficiency": "88.7", "route_points": "M1,M2,M3,M4,M5,M6,M7,M8", "description": "M区商品库存盘点"}, {"id": "AI-20240116-007", "name": "促销区检查", "type": "专项巡检", "status": "计划中", "start_time": "2024-01-16 15:45:00", "end_time": null, "duration": "0", "location": "N区-促销台015", "executor": "AI机器人", "completion_rate": 0, "anomaly_count": "0", "route_efficiency": "0", "route_points": "N1,N2,N3", "description": "促销区商品陈列检查"}, {"id": "AI-20240116-008", "name": "收银区巡检", "type": "日常巡检", "status": "计划中", "start_time": "2024-01-16 16:30:00", "end_time": null, "duration": "0", "location": "O区-收银台016", "executor": "AI机器人", "completion_rate": 0, "anomaly_count": "0", "route_efficiency": "0", "route_points": "O1,O2,O3,O4", "description": "收银区设备运行状态检查"}, {"id": "AI-20240117-001", "name": "日常巡检-C区", "type": "日常巡检", "status": "已完成", "start_time": "2024-01-17 08:30:00", "end_time": "2024-01-17 09:15:00", "duration": "45", "location": "C区-货架017", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "0", "route_efficiency": "96.1", "route_points": "C1,C2,C3,C4,C5,C6", "description": "C区货架商品陈列检查"}, {"id": "AI-20240117-002", "name": "冷链区温度巡检", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-17 09:30:00", "end_time": "2024-01-17 10:00:00", "duration": "30", "location": "P区-冷柜018", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "1", "route_efficiency": "93.7", "route_points": "P1,P2,P3,P4", "description": "冷链区温度监控检查"}, {"id": "AI-20240117-003", "name": "商品有效期检查", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-17 10:15:00", "end_time": "2024-01-17 11:00:00", "duration": "45", "location": "Q区-货架019", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "3", "route_efficiency": "89.2", "route_points": "Q1,Q2,Q3,Q4,Q5", "description": "商品有效期检查"}, {"id": "AI-20240117-004", "name": "商品陈列合规性检查", "type": "日常巡检", "status": "已完成", "start_time": "2024-01-17 11:15:00", "end_time": "2024-01-17 12:00:00", "duration": "45", "location": "R区-货架020", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "1", "route_efficiency": "94.8", "route_points": "R1,R2,R3,R4", "description": "商品陈列合规性检查"}, {"id": "AI-20240117-005", "name": "价格标签检查", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-17 13:00:00", "end_time": "2024-01-17 13:45:00", "duration": "45", "location": "S区-货架021", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "2", "route_efficiency": "92.5", "route_points": "S1,S2,S3,S4,S5", "description": "价格标签一致性检查"}, {"id": "AI-20240117-006", "name": "库存盘点", "type": "定期巡检", "status": "已完成", "start_time": "2024-01-17 14:00:00", "end_time": "2024-01-17 15:30:00", "duration": "90", "location": "T区-货架022", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "4", "route_efficiency": "88.9", "route_points": "T1,T2,T3,T4,T5,T6,T7,T8", "description": "T区商品库存盘点"}, {"id": "AI-20240117-007", "name": "促销区检查", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-17 15:45:00", "end_time": "2024-01-17 16:15:00", "duration": "30", "location": "U区-促销台023", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "0", "route_efficiency": "97.6", "route_points": "U1,U2,U3", "description": "促销区商品陈列检查"}, {"id": "AI-20240117-008", "name": "收银区巡检", "type": "日常巡检", "status": "已完成", "start_time": "2024-01-17 16:30:00", "end_time": "2024-01-17 17:00:00", "duration": "30", "location": "V区-收银台024", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "0", "route_efficiency": "98.2", "route_points": "V1,V2,V3,V4", "description": "收银区设备运行状态检查"}, {"id": "AI-20240118-001", "name": "日常巡检-D区", "type": "日常巡检", "status": "已完成", "start_time": "2024-01-18 08:30:00", "end_time": "2024-01-18 09:15:00", "duration": "45", "location": "D区-货架025", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "1", "route_efficiency": "95.3", "route_points": "D1,D2,D3,D4,D5,D6", "description": "D区货架商品陈列检查"}, {"id": "AI-20240118-002", "name": "生鲜区温度巡检", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-18 09:30:00", "end_time": "2024-01-18 10:00:00", "duration": "30", "location": "W区-生鲜柜026", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "0", "route_efficiency": "96.4", "route_points": "W1,W2,W3,W4", "description": "生鲜区温度监控检查"}, {"id": "AI-20240118-003", "name": "商品有效期检查", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-18 10:15:00", "end_time": "2024-01-18 11:00:00", "duration": "45", "location": "X区-货架027", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "2", "route_efficiency": "90.7", "route_points": "X1,X2,X3,X4,X5", "description": "商品有效期检查"}, {"id": "AI-20240118-004", "name": "商品陈列合规性检查", "type": "日常巡检", "status": "已完成", "start_time": "2024-01-18 11:15:00", "end_time": "2024-01-18 12:00:00", "duration": "45", "location": "Y区-货架028", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "0", "route_efficiency": "95.9", "route_points": "Y1,Y2,Y3,Y4", "description": "商品陈列合规性检查"}, {"id": "AI-20240118-005", "name": "价格标签检查", "type": "专项巡检", "status": "已完成", "start_time": "2024-01-18 13:00:00", "end_time": "2024-01-18 13:45:00", "duration": "45", "location": "Z区-货架029", "executor": "AI机器人", "completion_rate": 100, "anomaly_count": "3", "route_efficiency": "91.8", "route_points": "Z1,Z2,Z3,Z4,Z5", "description": "价格标签一致性检查"}, {"id": "AI-20240118-006", "name": "库存盘点", "type": "定期巡检", "status": "进行中", "start_time": "2024-01-18 14:00:00", "end_time": null, "duration": "0", "location": "AA区-货架030", "executor": "AI机器人", "completion_rate": 75, "anomaly_count": "2", "route_efficiency": "89.5", "route_points": "AA1,AA2,AA3,AA4,AA5,AA6,AA7,AA8", "description": "AA区商品库存盘点"}, {"id": "AI-20240118-007", "name": "促销区检查", "type": "专项巡检", "status": "计划中", "start_time": "2024-01-18 15:45:00", "end_time": null, "duration": "0", "location": "BB区-促销台031", "executor": "AI机器人", "completion_rate": 0, "anomaly_count": "0", "route_efficiency": "0", "route_points": "BB1,BB2,BB3", "description": "促销区商品陈列检查"}, {"id": "AI-20240118-008", "name": "收银区巡检", "type": "日常巡检", "status": "计划中", "start_time": "2024-01-18 16:30:00", "end_time": null, "duration": "0", "location": "CC区-收银台032", "executor": "AI机器人", "completion_rate": 0, "anomaly_count": "0", "route_efficiency": "0", "route_points": "CC1,CC2,CC3,CC4", "description": "收银区设备运行状态检查"}]}